import 'package:flutter/material.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:hive/hive.dart';

/// Utility class for authentication-related operations
class AuthUtils {
  static Box<UserEntity>? _userBox;

  /// Initialize the user box
  static void initialize() {
    _userBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
  }

  /// Get current user from storage
  static UserEntity? getCurrentUser() {
    _userBox ??= Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    return _userBox?.get(AppConstants.kMyProfileKey);
  }

  /// Check if user is authenticated (not a guest)
  static bool isAuthenticated() {
    final user = getCurrentUser();
    return user != null && !user.isGuest && user.token.isNotEmpty;
  }

  /// Check if user is in guest mode
  static bool isGuest() {
    final user = getCurrentUser();
    return user?.isGuest ?? true;
  }

  /// Check if user has a valid token
  static bool hasValidToken() {
    final user = getCurrentUser();
    return user?.token.isNotEmpty ?? false;
  }

  /// Get user token for API calls
  static String? getUserToken() {
    final user = getCurrentUser();
    return user?.token;
  }

  /// Get user ID
  static int? getUserId() {
    final user = getCurrentUser();
    return user?.id;
  }

  /// Check if user is a hoster (deprecated - use isInHosterMode instead)
  @Deprecated('Use isInHosterMode() instead')
  static bool isHoster() {
    return isInHosterMode();
  }

  /// Check if user is in hoster mode
  static bool isInHosterMode() {
    final user = getCurrentUser();
    return user?.isHosterMode ?? false;
  }

  /// Get user full name
  static String getUserName() {
    final user = getCurrentUser();
    return user?.fullName ?? 'Guest User';
  }

  /// Get user email
  static String getUserEmail() {
    final user = getCurrentUser();
    return user?.email ?? '';
  }

  /// Get user phone
  static String getUserPhone() {
    final user = getCurrentUser();
    return user?.phone ?? '';
  }

  /// Check if user phone is verified
  static bool isPhoneVerified() {
    final user = getCurrentUser();
    return user?.otpApproved ?? false;
  }

  /// Update user data in storage
  static Future<void> updateUser(UserEntity updatedUser) async {
    _userBox ??= Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    debugPrint('AuthUtils: Saving user - isGuest: ${updatedUser.isGuest}, hasToken: ${updatedUser.token.isNotEmpty}, isHosterMode: ${updatedUser.isHosterMode}');
    await _userBox?.put(AppConstants.kMyProfileKey, updatedUser);

    // Verify the save worked
    final savedUser = _userBox?.get(AppConstants.kMyProfileKey);
    debugPrint('AuthUtils: Verified saved user - isGuest: ${savedUser?.isGuest}, hasToken: ${savedUser?.token?.isNotEmpty}, isHosterMode: ${savedUser?.isHosterMode}');
  }

  /// Clear user data (logout)
  static Future<void> clearUserData() async {
    _userBox ??= Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    await _userBox?.clear();
  }

  /// Check if user needs to login for a specific action
  static bool needsLoginForAction(String action) {
    switch (action) {
      case 'reservation':
      case 'favorite':
      case 'review':
      case 'host':
        return isGuest();
      case 'view':
      case 'search':
      case 'browse':
        return false; // These actions don't require login
      default:
        return isGuest();
    }
  }

  /// Get authentication headers for API calls
  static Map<String, String> getAuthHeaders() {
    final token = getUserToken();
    if (token != null && token.isNotEmpty) {
      return {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };
    }
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  /// Check if user can perform reservation
  static bool canMakeReservation() {
    return hasValidToken(); // Both guest and authenticated users can make reservations
  }

  /// Check if user can add to favorites
  static bool canAddToFavorites() {
    return isAuthenticated(); // Only authenticated users can add favorites
  }

  /// Check if user can write reviews
  static bool canWriteReviews() {
    return isAuthenticated(); // Only authenticated users can write reviews
  }

  /// Check if user can host properties
  static bool canHostProperties() {
    return isAuthenticated() && isInHosterMode();
  }

  /// Get user display name for UI
  static String getDisplayName() {
    final user = getCurrentUser();
    if (user == null || user.isGuest) {
      return 'ضيف';
    }
    return user.fullName.isNotEmpty ? user.fullName : user.username;
  }

  /// Get user avatar URL
  static String? getUserAvatarUrl() {
    final user = getCurrentUser();
    return user?.image.isNotEmpty == true ? user?.image : null;
  }

  /// Check if user profile is complete
  static bool isProfileComplete() {
    final user = getCurrentUser();
    if (user == null || user.isGuest) return false;

    return user.fullName.isNotEmpty &&
           user.email.isNotEmpty &&
           user.phone.isNotEmpty &&
           user.otpApproved;
  }

  /// Check if user profile is complete for host mode (includes additional fields)
  static bool isProfileCompleteForHostMode() {
    final user = getCurrentUser();
    if (user == null || user.isGuest) return false;

    return user.fullName.isNotEmpty &&
           user.email.isNotEmpty &&
           user.phone.isNotEmpty &&
           user.bio.isNotEmpty &&
           user.birthdate.isNotEmpty &&
           user.gender > 0 &&
           user.otpApproved;
  }

  /// Get missing profile fields
  static List<String> getMissingProfileFields() {
    final user = getCurrentUser();
    final missing = <String>[];

    if (user == null || user.isGuest) {
      return ['login_required'];
    }

    if (user.fullName.isEmpty) missing.add('full_name');
    if (user.email.isEmpty) missing.add('email');
    if (user.phone.isEmpty) missing.add('phone');
    if (!user.otpApproved) missing.add('phone_verification');

    return missing;
  }

  /// Get missing profile fields for host mode
  static List<String> getMissingProfileFieldsForHostMode() {
    final user = getCurrentUser();
    final missing = <String>[];

    if (user == null || user.isGuest) {
      return ['login_required'];
    }

    if (user.fullName.isEmpty) missing.add('full_name');
    if (user.email.isEmpty) missing.add('email');
    if (user.phone.isEmpty) missing.add('phone');
    if (user.bio.isEmpty) missing.add('bio');
    if (user.birthdate.isEmpty) missing.add('birthdate');
    if (user.gender <= 0) missing.add('gender');
    //if (!user.otpApproved) missing.add('phone_verification');

    return missing;
  }

  /// Format user type for display
  static String getUserTypeDisplay() {
    final user = getCurrentUser();
    if (user == null || user.isGuest) return 'ضيف';

    // All authenticated users are now regular users, differentiated by hoster mode
    return user.isHosterMode ? 'مضيف' : 'مستخدم';
  }

  /// Check if user can access feature
  static bool canAccessFeature(String feature) {
    switch (feature) {
      case 'reservations_list':
      case 'favorites_list':
      case 'profile':
        return hasValidToken();
      case 'host_dashboard':
        return canHostProperties();
      case 'browse':
      case 'search':
      case 'view_details':
        return true;
      default:
        return hasValidToken();
    }
  }
}
